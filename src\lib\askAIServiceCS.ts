// AI Service for Computer Science Engineering - GATE CS specific
import { geminiService } from './geminiService';
import { webSearchService } from './webSearchService';
import { youtubeService } from './youtubeService';
import { AIResponseCS, QueryFiltersCS, ChatMessageCS } from '../types/askAICS';

class AskAIServiceCS {
  private readonly timeout = 30000; // 30 seconds timeout

  async processQuery(
    question: string,
    filters: QueryFiltersCS,
    conversationHistory: ChatMessageCS[] = []
  ): Promise<AIResponseCS> {
    try {
      console.log('🤖 AskAI CS: Processing query:', question);
      console.log('🔧 Filters:', filters);

      // Generate unique ID for this response
      const responseId = `ai_cs_response_${Date.now()}`;

      // Parallel execution of different search types with timeout
      const [aiExplanation, webSources, youtubeVideos, images] = await Promise.all([
        this.withTimeout(this.generateCSAIExplanation(question, filters, conversationHistory), 15000),
        filters.includeWebSearch ? this.withTimeout(this.searchWebSources(question, filters), 10000) : Promise.resolve([]),
        filters.includeYouTube ? this.withTimeout(this.searchYouTubeVideos(question), 10000) : Promise.resolve([]),
        filters.includeImages ? this.withTimeout(this.searchImages(question), 8000) : Promise.resolve([])
      ]);

      // Parse AI explanation to extract structured data
      const structuredResponse = this.parseCSAIResponse(aiExplanation);

      const response: AIResponseCS = {
        id: responseId,
        answer: structuredResponse.answer,
        explanation: structuredResponse.explanation,
        keyPoints: structuredResponse.keyPoints,
        algorithms: structuredResponse.algorithms || [],
        codeExamples: structuredResponse.codeExamples || [],
        stepByStepSolution: structuredResponse.stepByStepSolution,
        webSources: Array.isArray(webSources) ? webSources.slice(0, 6) : [], // Limit to top 6 sources
        youtubeVideos: Array.isArray(youtubeVideos) ? youtubeVideos.slice(0, 4) : [], // Limit to top 4 videos
        images: Array.isArray(images) ? images.slice(0, 8) : [], // Limit to top 8 images
        relatedTopics: structuredResponse.relatedTopics,
        difficulty: this.assessDifficulty(question),
        gateRelevance: this.assessGATECSRelevance(question),
        timestamp: new Date().toISOString()
      };

      console.log('✅ AskAI CS: Response generated successfully');
      return response;

    } catch (error) {
      console.error('❌ AskAI CS: Error processing query:', error);

      // Return a fallback response instead of throwing
      return this.getFallbackResponse(question, error);
    }
  }

  private getFallbackResponse(question: string, error: any): AIResponseCS {
    const responseId = `ai_cs_fallback_${Date.now()}`;

    return {
      id: responseId,
      answer: `I apologize, but I encountered an error while processing your CS question: "${question}". Please try rephrasing your question or try again later.`,
      explanation: `There was a technical issue with the AI service. This could be due to network connectivity, API limits, or service maintenance. Your question about "${question}" is valid and I'd be happy to help once the service is restored.`,
      keyPoints: [
        'The AI service encountered a technical error',
        'Your question is valid and important for GATE CS preparation',
        'Try rephrasing the question or asking again later',
        'Consider breaking complex questions into smaller parts'
      ],
      algorithms: [],
      codeExamples: [],
      stepByStepSolution: undefined,
      webSources: [],
      youtubeVideos: [],
      images: [],
      relatedTopics: this.extractTopicsFromQuestion(question),
      difficulty: this.assessDifficulty(question),
      gateRelevance: this.assessGATECSRelevance(question),
      timestamp: new Date().toISOString()
    };
  }

  private extractTopicsFromQuestion(question: string): string[] {
    const csTopics = [
      'Data Structures', 'Algorithms', 'Programming', 'Computer Organization',
      'Operating Systems', 'Databases', 'Computer Networks', 'Theory of Computation',
      'Compiler Design', 'Digital Logic', 'Software Engineering', 'Machine Learning'
    ];

    const lowerQuestion = question.toLowerCase();
    return csTopics.filter(topic =>
      lowerQuestion.includes(topic.toLowerCase()) ||
      topic.toLowerCase().split(' ').some(word => lowerQuestion.includes(word))
    ).slice(0, 3);
  }

  private async generateCSAIExplanation(
    question: string,
    filters: QueryFiltersCS,
    conversationHistory: ChatMessageCS[]
  ): Promise<string> {
    const prompt = this.buildCSPrompt(question, filters, conversationHistory);

    try {
      const response = await geminiService.makeRequest(prompt, {
        temperature: 0.7,
        maxTokens: 2000,
        systemRole: 'computer_science_expert'
      });

      return response;
    } catch (error) {
      console.error('Error generating CS AI explanation:', error);
      throw error;
    }
  }

  private buildCSPrompt(question: string, filters: QueryFiltersCS, conversationHistory: ChatMessageCS[]): string {
    // Build context from conversation history
    let contextString = '';
    if (conversationHistory.length > 0) {
      const recentMessages = conversationHistory.slice(-6); // Last 3 exchanges
      contextString = `\nConversation Context:\n${recentMessages.map(msg =>
        `${msg.type === 'user' ? 'Student' : 'AI'}: ${msg.content}`
      ).join('\n')}\n`;
    }

    const languageContext = filters.programmingLanguage ?
      `Focus on ${filters.programmingLanguage} programming language examples.` :
      'Use appropriate programming language examples (C++, Java, Python preferred for GATE CS).';

    const topicContext = filters.topicArea ?
      `This question is related to ${filters.topicArea}.` : '';

    return `You are an expert GATE Computer Science Engineering tutor specializing in doubt resolution and concept explanation.
${contextString}
Student Question: "${question}"

${languageContext}
${topicContext}

Please provide a comprehensive, natural language response that includes:

1. **Direct Answer**: Start with a clear, direct answer to the question in 2-3 sentences.

2. **Detailed Explanation**: Provide a detailed explanation of the concept (200-300 words) using simple, student-friendly language.

3. **Key Points**: List 3-4 important key points about the topic.

4. **Algorithms** (if applicable): Include relevant algorithms with:
   - Algorithm name and description
   - Time and space complexity analysis
   - Pseudocode or step-by-step approach
   - Practical applications

5. **Code Examples** (if applicable): Provide working code examples with:
   - Programming language used
   - Complete, runnable code
   - Expected output
   - Line-by-line explanation

6. **Related Topics**: Mention 2-3 related GATE CS topics.

Focus on GATE Computer Science syllabus including:
- Data Structures and Algorithms
- Programming and Data Structures
- Computer Organization and Architecture
- Theory of Computation
- Compiler Design
- Operating Systems
- Databases
- Computer Networks
- Digital Logic
- Software Engineering
- Web Technologies
- Machine Learning basics

Provide practical examples, code snippets, and algorithmic solutions where applicable.
Use clear, student-friendly language and explain concepts from first principles.`;
  }

  private async searchWebSources(question: string, filters: QueryFiltersCS) {
    try {
      const searchQuery = `${question} GATE Computer Science Engineering ${filters.topicArea || ''}`;
      const result = await webSearchService.searchArticles(searchQuery, { num: 6 });
      return result.items.map(item => ({
        title: item.title,
        url: item.link,
        snippet: item.snippet,
        domain: item.displayLink,
        relevanceScore: 0.8
      }));
    } catch (error) {
      console.error('Error searching web sources for CS:', error);
      return [];
    }
  }

  private async searchYouTubeVideos(question: string) {
    try {
      const searchQuery = `${question} GATE Computer Science tutorial explanation`;
      const result = await youtubeService.searchVideos(searchQuery, 4);
      return result.videos.map(video => ({
        title: video.title,
        url: video.url,
        thumbnail: video.thumbnail,
        channel: video.channelTitle,
        duration: video.duration || '0:00',
        views: video.viewCount || '0 views',
        relevanceScore: 0.8
      }));
    } catch (error) {
      console.error('Error searching YouTube videos for CS:', error);
      return [];
    }
  }

  private async searchImages(question: string) {
    try {
      const searchQuery = `${question} computer science diagram algorithm flowchart`;
      // For now, return empty array as image search might not be implemented
      // TODO: Implement image search in webSearchService
      return [];
    } catch (error) {
      console.error('Error searching images for CS:', error);
      return [];
    }
  }

  private withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<T>((_, reject) =>
        setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs)
      )
    ]);
  }

  private parseCSAIResponse(aiResponse: string): {
    answer: string;
    explanation: string;
    keyPoints: string[];
    algorithms: any[];
    codeExamples: any[];
    relatedTopics: string[];
    stepByStepSolution?: any;
  } {
    try {
      // First try to parse as JSON (for backward compatibility)
      let jsonString = '';

      // Pattern 1: Look for JSON block between ```json and ```
      const jsonBlockMatch = aiResponse.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonBlockMatch) {
        jsonString = jsonBlockMatch[1];
      } else {
        // Pattern 2: Look for any JSON object
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          jsonString = jsonMatch[0];
        }
      }

      if (jsonString) {
        try {
          // Clean up the JSON string
          jsonString = jsonString
            .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
            .replace(/,\s*}/g, '}') // Remove trailing commas
            .replace(/,\s*]/g, ']'); // Remove trailing commas in arrays

          const parsed = JSON.parse(jsonString);

          return {
            answer: typeof parsed.answer === 'string' ? parsed.answer : 'AI explanation not available',
            explanation: typeof parsed.explanation === 'string' ? parsed.explanation : 'Detailed explanation not available',
            keyPoints: Array.isArray(parsed.keyPoints) ? parsed.keyPoints : [],
            algorithms: Array.isArray(parsed.algorithms) ? parsed.algorithms : [],
            codeExamples: Array.isArray(parsed.codeExamples) ? parsed.codeExamples : [],
            relatedTopics: Array.isArray(parsed.relatedTopics) ? parsed.relatedTopics : [],
            stepByStepSolution: parsed.stepByStepSolution || undefined
          };
        } catch (jsonError) {
          // If JSON parsing fails, fall through to natural language parsing
        }
      }

      // Parse natural language response
      return this.parseNaturalLanguageCSResponse(aiResponse);
    } catch (error) {
      console.error('Error parsing CS AI response:', error);
      console.error('Raw response:', aiResponse);

      // Try to extract at least the basic answer from the response
      const fallbackAnswer = this.extractFallbackAnswer(aiResponse);

      return {
        answer: fallbackAnswer,
        explanation: 'There was an error processing the structured AI response. The basic answer is provided above.',
        keyPoints: [],
        algorithms: [],
        codeExamples: [],
        relatedTopics: [],
        stepByStepSolution: undefined
      };
    }
  }

  private parseNaturalLanguageCSResponse(response: string): {
    answer: string;
    explanation: string;
    keyPoints: string[];
    algorithms: any[];
    codeExamples: any[];
    relatedTopics: string[];
    stepByStepSolution?: any;
  } {
    // Extract answer (usually the first paragraph or sentences)
    const answerMatch = response.match(/^(.*?)(?:\n\n|\n(?=\d+\.|\*\*|#))/s);
    const answer = answerMatch ? answerMatch[1].trim() : response.substring(0, 200) + '...';

    // Extract explanation (look for detailed explanation section)
    const explanationMatch = response.match(/(?:explanation|detailed explanation)[:\s]*\n?(.*?)(?=\n(?:\d+\.|key points|algorithms|code|related))/is);
    const explanation = explanationMatch ? explanationMatch[1].trim() : response;

    // Extract key points
    const keyPoints = this.extractCSKeyPoints(response);

    // Extract algorithms
    const algorithms = this.extractAlgorithms(response);

    // Extract code examples
    const codeExamples = this.extractCodeExamples(response);

    // Extract related topics
    const relatedTopics = this.extractCSRelatedTopics(response);

    return {
      answer,
      explanation,
      keyPoints,
      algorithms,
      codeExamples,
      relatedTopics,
      stepByStepSolution: undefined
    };
  }

  private extractCSKeyPoints(response: string): string[] {
    const keyPoints: string[] = [];

    // Look for numbered or bulleted key points
    const patterns = [
      /key points?[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is,
      /important points?[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is,
      /(?:^|\n)(?:\d+\.\s*)(.*?)(?=\n\d+\.|\n[A-Z]|\n$)/gm
    ];

    for (const pattern of patterns) {
      const match = response.match(pattern);
      if (match) {
        const pointsText = match[1] || match[0];
        const points = pointsText.split(/\n/).map(p => p.replace(/^[-*•\d.\s]+/, '').trim()).filter(p => p.length > 0);
        keyPoints.push(...points);
        break;
      }
    }

    // If no structured key points found, extract from content
    if (keyPoints.length === 0) {
      const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 20);
      keyPoints.push(...sentences.slice(0, 4).map(s => s.trim()));
    }

    return keyPoints.slice(0, 4);
  }

  private extractAlgorithms(response: string): any[] {
    const algorithms: any[] = [];

    // Look for algorithm sections
    const algorithmMatch = response.match(/algorithms?[:\s]*\n?(.*?)(?=\n(?:code|related|key))/is);
    if (algorithmMatch) {
      const algorithmText = algorithmMatch[1];
      // Simple extraction - could be enhanced
      algorithms.push({
        name: 'Algorithm',
        description: algorithmText.trim(),
        timeComplexity: 'O(n)',
        spaceComplexity: 'O(1)',
        pseudocode: '',
        applications: []
      });
    }

    return algorithms;
  }

  private extractCodeExamples(response: string): any[] {
    const codeExamples: any[] = [];

    // Look for code blocks
    const codeMatches = response.match(/```(\w+)?\n([\s\S]*?)```/g);
    if (codeMatches) {
      codeMatches.forEach((codeBlock, index) => {
        const languageMatch = codeBlock.match(/```(\w+)/);
        const codeMatch = codeBlock.match(/```(?:\w+)?\n([\s\S]*?)```/);

        if (codeMatch) {
          codeExamples.push({
            language: languageMatch ? languageMatch[1] : 'text',
            code: codeMatch[1].trim(),
            description: `Code example ${index + 1}`,
            output: '',
            explanation: ''
          });
        }
      });
    }

    return codeExamples;
  }

  private extractCSRelatedTopics(response: string): string[] {
    const relatedTopics: string[] = [];

    // Look for related topics section
    const patterns = [
      /related topics?[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is,
      /see also[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is,
      /other topics?[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/is
    ];

    for (const pattern of patterns) {
      const match = response.match(pattern);
      if (match) {
        const topicsText = match[1];
        const topics = topicsText.split(/\n/).map(t => t.replace(/^[-*•\s]+/, '').trim()).filter(t => t.length > 0);
        relatedTopics.push(...topics);
        break;
      }
    }

    return relatedTopics.slice(0, 3);
  }

  private extractFallbackAnswer(response: string): string {
    // Try to extract a meaningful answer from the raw response
    const lines = response.split('\n').filter(line => line.trim().length > 0);

    // Look for lines that seem like answers
    for (const line of lines) {
      if (line.length > 50 && line.length < 300) {
        return line.trim();
      }
    }

    // If no good line found, return first 200 characters
    return response.substring(0, 200).trim() + '...';
  }

  private assessDifficulty(question: string): 'basic' | 'intermediate' | 'advanced' {
    const basicKeywords = ['what is', 'define', 'basic', 'introduction', 'simple'];
    const advancedKeywords = ['optimize', 'complex', 'advanced', 'design', 'implement', 'algorithm analysis'];

    const lowerQuestion = question.toLowerCase();

    if (basicKeywords.some(keyword => lowerQuestion.includes(keyword))) {
      return 'basic';
    } else if (advancedKeywords.some(keyword => lowerQuestion.includes(keyword))) {
      return 'advanced';
    }

    return 'intermediate';
  }

  private assessGATECSRelevance(question: string): number {
    const gateCSTopics = [
      'data structures', 'algorithms', 'programming', 'computer organization',
      'operating systems', 'databases', 'computer networks', 'theory of computation',
      'compiler design', 'digital logic', 'software engineering', 'machine learning'
    ];

    const lowerQuestion = question.toLowerCase();
    const relevantTopics = gateCSTopics.filter(topic => lowerQuestion.includes(topic));

    // Base relevance + bonus for topic matches
    return Math.min(10, 5 + relevantTopics.length * 1.5);
  }

  // Test method to verify CS AI service functionality
  async testService(): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      console.log('🧪 Testing CS AI Service...');

      const testQuery = 'What is binary search algorithm?';
      const testFilters: QueryFiltersCS = {
        includeWebSearch: false,
        includeYouTube: false,
        includeImages: false,
        includeCodeExamples: true,
        includeAlgorithms: true,
        programmingLanguage: 'C++',
        topicArea: 'Algorithms',
        difficultyLevel: 'intermediate'
      };

      const startTime = Date.now();
      const response = await this.processQuery(testQuery, testFilters, []);
      const endTime = Date.now();

      if (response && response.answer && response.answer.length > 10) {
        return {
          success: true,
          message: `CS AI Service is working! Response generated in ${endTime - startTime}ms.`,
          details: {
            responseLength: response.answer.length,
            difficulty: response.difficulty,
            gateRelevance: response.gateRelevance,
            hasAlgorithms: response.algorithms?.length > 0,
            hasCodeExamples: response.codeExamples?.length > 0,
            processingTime: endTime - startTime
          }
        };
      } else {
        return {
          success: false,
          message: 'CS AI Service returned invalid response',
          details: { response }
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `CS AI Service test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { error }
      };
    }
  }

  // Check if the service is properly configured
  isConfigured(): boolean {
    return geminiService.isConfigured();
  }
}

export const askAIServiceCS = new AskAIServiceCS();
